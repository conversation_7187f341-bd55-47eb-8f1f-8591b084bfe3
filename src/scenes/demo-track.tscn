[gd_scene load_steps=5 format=3 uid="uid://7o8wv0nvc7p7"]

[ext_resource type="Texture2D" uid="uid://cit0dfy118dyi" path="res://assets/sprites/test-track.png" id="1_ebpgw"]
[ext_resource type="Texture2D" uid="uid://qhf204tgjq6v" path="res://assets/sprites/test-car.png" id="2_dql50"]
[ext_resource type="Script" uid="uid://4obiongrg288" path="res://src/scenes/Racecar.cs" id="2_scayd"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_dql50"]
radius = 12.0
height = 40.0

[node name="Demo Track" type="Node2D"]

[node name="Test-track" type="Sprite2D" parent="."]
position = Vector2(0, -5)
texture = ExtResource("1_ebpgw")

[node name="Racecar" type="CharacterBody2D" parent="."]
position = Vector2(131, 107)
script = ExtResource("2_scayd")

[node name="Test-car" type="Sprite2D" parent="Racecar"]
rotation = 3.14159
texture = ExtResource("2_dql50")
hframes = 3

[node name="Camera2D" type="Camera2D" parent="Racecar"]
zoom = Vector2(2, 2)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Racecar"]
shape = SubResource("CapsuleShape2D_dql50")
